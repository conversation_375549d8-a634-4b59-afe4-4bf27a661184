import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Platform,
  Alert,
  Dimensions
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../context/auth';
import { useTheme } from '../../../context/theme';
import { supabase } from '../../../lib/supabase';
import SimpleSafeContainer from '../../../app/components/SimpleSafeContainer';
import SimpleScrollContainer from '../../../app/components/SimpleScrollContainer';
import SimpleCard from '../../../app/components/SimpleCard';

export default function EmergencyScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { theme } = useTheme();
  const [loading, setLoading] = useState<{[key: string]: boolean}>({});

  // Appel d'urgence direct
  const emergencyCall = () => {
    const phoneNumber = Platform.OS === 'android' ? 'tel:112' : 'telprompt:112';
    Linking.openURL(phoneNumber);
  };

  // Activation du mode SOS complet
  const activateSOS = () => {
    router.push('/(app)/(emergency)/emergency');
  };

  // Partage de position avec contacts d'urgence
  const shareLocation = async () => {
    setLoading({...loading, location: true});
    try {
      // Vérifier si l'utilisateur a des contacts d'urgence
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('emergency_contact')
        .eq('id', user?.id)
        .single();

      if (error) throw error;

      if (!profile?.emergency_contact) {
        Alert.alert(
          "Aucun contact d'urgence",
          "Veuillez configurer vos contacts d'urgence dans votre profil.",
          [
            { text: "Configurer maintenant", onPress: () => router.push('/(tabs)/profile') },
            { text: "Annuler" }
          ]
        );
      } else {
        // Ici, intégrer la logique pour partager la position
        // Pour l'instant, nous affichons une notification de succès
        Alert.alert(
          "Position partagée",
          "Votre position a été envoyée à vos contacts d'urgence."
        );
      }
    } catch (error) {
      console.error("Erreur lors du partage de la position:", error);
      Alert.alert("Erreur", "Impossible de partager votre position actuellement.");
    } finally {
      setLoading({...loading, location: false});
    }
  };

  // Ouvrir les contacts d'urgence
  const openEmergencyContacts = async () => {
    setLoading({...loading, contacts: true});
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('emergency_contact')
        .eq('id', user?.id)
        .single();

      if (error) throw error;

      if (!profile?.emergency_contact) {
        Alert.alert(
          "Aucun contact d'urgence",
          "Souhaitez-vous configurer vos contacts d'urgence maintenant?",
          [
            { text: "Oui", onPress: () => router.push('/(tabs)/profile') },
            { text: "Non" }
          ]
        );
      } else {
        // Format du contact: {"name":"Nom","phone":"0123456789"}
        const contact = JSON.parse(profile.emergency_contact);
        const phoneNumber = Platform.OS === 'android' ? `tel:${contact.phone}` : `telprompt:${contact.phone}`;
        Linking.openURL(phoneNumber);
      }
    } catch (error) {
      console.error("Erreur lors de l'accès aux contacts d'urgence:", error);
      Alert.alert("Erreur", "Impossible d'accéder à vos contacts d'urgence.");
    } finally {
      setLoading({...loading, contacts: false});
    }
  };

  // Afficher les instructions de premiers secours
  const showFirstAid = () => {
    router.push('/(app)/(emergency)/first-aid');
  };

  // Obtenir les dimensions de l'écran
  const screenHeight = Dimensions.get('window').height;

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Urgence',
          headerStyle: {
            backgroundColor: '#FF3B30',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
      <SimpleSafeContainer
        backgroundColor="#FF3B30"
        statusBarStyle="light-content"
        edges={['top', 'left', 'right']}
      >
        <View style={styles.header}>
          <Text style={styles.title}>SOS - URGENCE</Text>
          <Text style={styles.subtitle}>Besoin d'aide immédiate ?</Text>
        </View>

        <View style={styles.buttonsContainer}>
          <SimpleCard
            padding={16}
            margin={8}
            borderRadius={12}
            backgroundColor="#FF3B30"
            style={styles.emergencyButton}
            onPress={emergencyCall}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="call" size={30} color="white" />
              <Text style={styles.buttonText}>APPELER LE 112</Text>
            </View>
          </SimpleCard>

          <SimpleCard
            padding={16}
            margin={8}
            borderRadius={12}
            backgroundColor="#D32F2F"
            style={[styles.emergencyButton, styles.sosButton]}
            onPress={activateSOS}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="alert-circle" size={30} color="white" />
              <Text style={styles.buttonText}>ACTIVER LE MODE SOS</Text>
            </View>
          </SimpleCard>
        </View>

        <SimpleScrollContainer
          style={styles.optionsContainer}
          contentContainerStyle={styles.scrollContent}
        >
          <Text style={styles.sectionTitle}>Options d'urgence</Text>

          <TouchableOpacity style={styles.option} onPress={showFirstAid}>
            <View style={styles.optionIcon}>
              <Ionicons name="medkit" size={24} color="#FF3B30" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Premiers secours</Text>
              <Text style={styles.optionDescription}>Instructions de base pour les premiers soins</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.option}
            onPress={shareLocation}
            disabled={loading.location}
          >
            <View style={styles.optionIcon}>
              <Ionicons name={loading.location ? "hourglass" : "location"} size={24} color="#FF3B30" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Partager ma position</Text>
              <Text style={styles.optionDescription}>Envoyer ma position GPS aux contacts d'urgence</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.option}
            onPress={openEmergencyContacts}
            disabled={loading.contacts}
          >
            <View style={styles.optionIcon}>
              <Ionicons name={loading.contacts ? "hourglass" : "people"} size={24} color="#FF3B30" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Contacts d'urgence</Text>
              <Text style={styles.optionDescription}>Appeler ou envoyer un message à vos contacts prédéfinis</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={styles.option} onPress={activateSOS}>
            <View style={styles.optionIcon}>
              <Ionicons name="alert-circle" size={24} color="#FF3B30" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Signal de détresse</Text>
              <Text style={styles.optionDescription}>Activer une alarme sonore et lumineuse</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.option}
            onPress={() => router.push('/(app)/(emergency)/history')}
          >
            <View style={styles.optionIcon}>
              <Ionicons name="time" size={24} color="#FF3B30" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Historique des alertes</Text>
              <Text style={styles.optionDescription}>Consulter vos alertes SOS des 30 derniers jours</Text>
            </View>
          </TouchableOpacity>

        </SimpleScrollContainer>
      </SimpleSafeContainer>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: 20,
  },
  buttonsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  emergencyButton: {
    backgroundColor: '#FF3B30',
    marginBottom: 10,
  },
  sosButton: {
    backgroundColor: '#D32F2F',
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  optionsContainer: {
    flex: 1,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  option: {
    flexDirection: 'row',
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  optionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FFE5E5',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
  },
});