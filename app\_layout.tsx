import '../polyfills'; // Import polyfills first to handle Node.js modules
import React, { useEffect } from 'react';
import { Slot } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Platform } from 'react-native';
import { ThemeProvider } from '../context/theme';
import { AuthProvider } from '../context/auth';
import { LocationProvider } from '../context/location';
import { setupGlobalErrorHandler } from '../utils/error-handler';
import { initDevLogger, devLogger } from '../utils/dev-logger';
import '../lib/activity-tracking'; // Initialiser le suivi des activités

// Configuration des logs pour le développement
if (__DEV__) {
  // S'assurer que console.log fonctionne correctement
  const originalLog = console.log;
  const originalWarn = console.warn;
  const originalError = console.error;
  const originalInfo = console.info;
  const originalDebug = console.debug;

  console.log = (...args) => {
    originalLog('[LOG]', new Date().toISOString(), ...args);
  };

  console.warn = (...args) => {
    originalWarn('[WARN]', new Date().toISOString(), ...args);
  };

  console.error = (...args) => {
    originalError('[ERROR]', new Date().toISOString(), ...args);
  };

  console.info = (...args) => {
    originalInfo('[INFO]', new Date().toISOString(), ...args);
  };

  console.debug = (...args) => {
    originalDebug('[DEBUG]', new Date().toISOString(), ...args);
  };

  // Log de démarrage
  console.log('🚀 Bomoko Mobile App Starting...', {
    platform: Platform.OS,
    version: Platform.Version,
    isDev: __DEV__
  });
}

export default function RootLayout() {
  // Initialize global error handler and logging
  useEffect(() => {
    try {
      // Initialiser le logger de développement en premier
      initDevLogger();

      // Puis le gestionnaire d'erreurs global
      setupGlobalErrorHandler();

      devLogger.success('Application initialized successfully');
      devLogger.info('Platform details', {
        os: Platform.OS,
        version: Platform.Version,
        isDev: __DEV__
      });

      // Test des différents types de logs
      devLogger.debug('Testing debug logs');
      devLogger.info('Testing info logs');
      devLogger.warn('Testing warning logs');

      // Test d'une erreur non critique
      setTimeout(() => {
        devLogger.error('Testing error logs (non-critical test)');
      }, 2000);

    } catch (error) {
      console.error('❌ Failed to initialize application:', error);
      devLogger.error('Application initialization failed', error);
    }
  }, []);

  return (
    <ThemeProvider>
      <AuthProvider>
        <LocationProvider>
          <StatusBar style="auto" />
          <Slot />
        </LocationProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
