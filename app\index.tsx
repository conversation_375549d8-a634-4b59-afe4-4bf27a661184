import React, { useEffect } from 'react';
import { Redirect } from 'expo-router';
import { useAuth } from '../context/auth';
import { 
  View, 
  Text, 
  ActivityIndicator, 
  StyleSheet 
} from 'react-native';
import { useTheme } from '../context/theme';

// Ce fichier sert de point d'entrée principal pour l'application
// Il redirige vers la page appropriée selon l'état d'authentification
export default function RootIndex() {
  const { user, initialized } = useAuth();
  const { theme } = useTheme();

  // Afficher un écran de chargement pendant l'initialisation
  if (!initialized) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          Chargement...
        </Text>
      </View>
    );
  }

  // Si l'utilisateur est connecté, rediriger vers la page d'accueil principale (tabs)
  if (user) {
    return <Redirect href="/(tabs)" />;
  }

  // Si l'utilisateur n'est pas connecté, rediriger vers la page de connexion
  return <Redirect href="/(auth)/login" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});
