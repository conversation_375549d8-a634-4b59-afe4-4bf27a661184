/**
 * Script de debugging avancé pour diagnostiquer les problèmes de logs
 * Exécuter avec: node debug-logs.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC DES LOGS - BOMOKO MOBILE');
console.log('=' .repeat(60));

// 1. Vérifier les fichiers de configuration
console.log('\n📁 Vérification des fichiers de configuration:');

const configFiles = [
  'metro.config.js',
  'app.config.js',
  'package.json',
  'polyfills.js',
  'app/_layout.tsx',
  'utils/dev-logger.ts',
  'utils/error-handler.ts'
];

configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Présent`);
  } else {
    console.log(`❌ ${file} - MANQUANT`);
  }
});

// 2. Vérifier le contenu des fichiers critiques
console.log('\n🔧 Vérification du contenu des fichiers:');

// Vérifier metro.config.js
if (fs.existsSync('metro.config.js')) {
  const metroConfig = fs.readFileSync('metro.config.js', 'utf8');
  if (metroConfig.includes('enhanceMiddleware')) {
    console.log('✅ Metro config - Configuration des logs présente');
  } else {
    console.log('⚠️ Metro config - Configuration des logs manquante');
  }
}

// Vérifier app.config.js
if (fs.existsSync('app.config.js')) {
  const appConfig = fs.readFileSync('app.config.js', 'utf8');
  if (appConfig.includes('developmentClient')) {
    console.log('✅ App config - Configuration developmentClient présente');
  } else {
    console.log('⚠️ App config - Configuration developmentClient manquante');
  }
}

// Vérifier package.json
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (packageJson.scripts['dev:verbose']) {
    console.log('✅ Package.json - Scripts de debugging présents');
  } else {
    console.log('⚠️ Package.json - Scripts de debugging manquants');
  }
}

// 3. Vérifier les variables d'environnement
console.log('\n🌍 Variables d\'environnement:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'non défini'}`);
console.log(`EXPO_PUBLIC_SUPABASE_URL: ${process.env.EXPO_PUBLIC_SUPABASE_URL ? 'défini' : 'non défini'}`);

// 4. Test des fonctions console
console.log('\n🧪 Test des fonctions console:');
try {
  console.log('✅ console.log fonctionne');
  console.info('✅ console.info fonctionne');
  console.warn('✅ console.warn fonctionne');
  console.error('✅ console.error fonctionne');
  console.debug('✅ console.debug fonctionne');
} catch (error) {
  console.error('❌ Erreur lors du test des fonctions console:', error);
}

// 5. Vérifier la structure des dossiers
console.log('\n📂 Structure des dossiers:');
const requiredDirs = ['app', 'utils', 'docs', 'node_modules'];
requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir}/ - Présent`);
  } else {
    console.log(`❌ ${dir}/ - MANQUANT`);
  }
});

// 6. Recommandations
console.log('\n💡 RECOMMANDATIONS:');
console.log('1. Si des fichiers sont manquants, vérifiez que toutes les modifications ont été appliquées');
console.log('2. Redémarrez complètement le serveur de développement');
console.log('3. Utilisez "npm run dev:clear" pour nettoyer le cache');
console.log('4. Vérifiez que votre terminal supporte les emojis et couleurs');
console.log('5. Assurez-vous que Expo Go est connecté au bon serveur');

// 7. Commandes utiles
console.log('\n⚡ COMMANDES UTILES:');
console.log('npm run dev:verbose    - Démarrer avec logs verbeux');
console.log('npm run dev:clear      - Démarrer en nettoyant le cache');
console.log('npm run test-logs      - Tester les fonctions de log');

// 8. Informations système
console.log('\n💻 INFORMATIONS SYSTÈME:');
console.log(`OS: ${process.platform}`);
console.log(`Node.js: ${process.version}`);
console.log(`Répertoire: ${process.cwd()}`);

console.log('\n' + '=' .repeat(60));
console.log('🏁 Diagnostic terminé');

// 9. Créer un rapport de diagnostic
const report = {
  timestamp: new Date().toISOString(),
  platform: process.platform,
  nodeVersion: process.version,
  directory: process.cwd(),
  configFiles: {},
  recommendations: []
};

configFiles.forEach(file => {
  report.configFiles[file] = fs.existsSync(file);
});

// Sauvegarder le rapport
fs.writeFileSync('diagnostic-report.json', JSON.stringify(report, null, 2));
console.log('📄 Rapport de diagnostic sauvegardé dans diagnostic-report.json');
