# Configuration des Logs - Bomoko Mobile

## Problème résolu

Les erreurs ne s'affichaient plus dans le terminal lors de l'utilisation d'Expo Go. Ce document explique les améliorations apportées pour corriger ce problème.

## Améliorations apportées

### 1. Configuration Metro améliorée (`metro.config.js`)
- Ajout de middleware pour logger les requêtes Metro
- Amélioration des logs du resolver pour le debugging
- Logs conditionnels basés sur `__DEV__`

### 2. Configuration Expo (`app.config.js`)
- Ajout de `developmentClient.silentLaunch: false`
- Assure que les logs ne sont pas supprimés au lancement

### 3. Logger de développement (`utils/dev-logger.ts`)
- Nouveau système de logs avec emojis et timestamps
- Historique des logs pour le debugging
- Fonctions utilitaires pour différents niveaux de log
- Statistiques des logs

### 4. Polyfills améliorés (`polyfills.js`)
- Préservation des fonctions console originales
- Évite l'écrasement des logs par les polyfills

### 5. Gestionnaire d'erreurs global (`utils/error-handler.ts`)
- Logs plus détaillés avec contexte
- Différenciation entre erreurs critiques et non-critiques
- Test automatique du gestionnaire d'erreurs

### 6. Layout principal (`app/_layout.tsx`)
- Initialisation du logger de développement
- Configuration des logs avec timestamps
- Tests automatiques des différents niveaux de log

## Utilisation

### Scripts disponibles

```bash
# Démarrer avec logs normaux
npm run dev

# Démarrer avec logs verbeux
npm run dev:verbose

# Démarrer en nettoyant le cache
npm run dev:clear

# Tester les logs
npm run test-logs
```

### Utilisation du logger de développement

```typescript
import { devLogger, logInfo, logError } from '../utils/dev-logger';

// Utilisation directe
devLogger.info('Message d\'information', { data: 'optional' });
devLogger.error('Message d\'erreur', error);
devLogger.success('Opération réussie');

// Utilisation avec fonctions utilitaires
logInfo('Message d\'information');
logError('Message d\'erreur');
```

### Fonctions globales de debugging

En mode développement, les fonctions suivantes sont disponibles globalement :

```javascript
// Afficher l'historique des logs
showLogHistory();

// Nettoyer l'historique
clearLogHistory();

// Afficher les statistiques
logStats();
```

## Types de logs disponibles

| Type | Emoji | Utilisation |
|------|-------|-------------|
| `debug` | 🐛 | Informations de debugging détaillées |
| `info` | ℹ️ | Informations générales |
| `warn` | ⚠️ | Avertissements |
| `error` | 🚨 | Erreurs |
| `success` | ✅ | Opérations réussies |

## Dépannage

### Les logs n'apparaissent toujours pas

1. **Vérifiez le mode développement**
   ```bash
   # Assurez-vous d'être en mode dev
   npm run dev
   ```

2. **Nettoyez le cache**
   ```bash
   npm run dev:clear
   ```

3. **Vérifiez la connexion Expo Go**
   - Assurez-vous que votre téléphone et ordinateur sont sur le même réseau
   - Redémarrez Expo Go
   - Reconnectez-vous au serveur de développement

4. **Vérifiez les filtres du terminal**
   - Certains terminaux peuvent filtrer les logs
   - Essayez un terminal différent

5. **Testez les logs**
   ```bash
   npm run test-logs
   ```

### Logs trop verbeux

Vous pouvez ajuster la configuration dans `utils/dev-logger.ts` :

```typescript
export const LOG_CONFIG = {
  enabled: __DEV__,
  showTimestamp: false, // Désactiver les timestamps
  showPlatform: false,  // Désactiver l'affichage de la plateforme
  colorEnabled: true,
  maxLogHistory: 50     // Réduire l'historique
};
```

## Fonctionnalités avancées

### Historique des logs

Le système garde un historique des logs que vous pouvez consulter :

```typescript
// Afficher les 20 derniers logs
devLogger.showHistory(20);

// Obtenir les statistiques
const stats = devLogger.getStats();
console.log(stats);
```

### Tests automatiques

Au démarrage de l'application, des tests automatiques vérifient que tous les types de logs fonctionnent correctement.

### Gestion des erreurs

Le gestionnaire d'erreurs global capture automatiquement :
- Les erreurs React Native fatales et non-fatales
- Les rejets de promesses non gérés
- Les erreurs globales

## Configuration personnalisée

Vous pouvez personnaliser le comportement des logs en modifiant :

1. `LOG_CONFIG` dans `utils/dev-logger.ts`
2. La configuration Metro dans `metro.config.js`
3. Les polyfills dans `polyfills.js`

## Support

Si vous rencontrez encore des problèmes avec les logs :

1. Vérifiez que toutes les modifications ont été appliquées
2. Redémarrez complètement le serveur de développement
3. Nettoyez le cache avec `npm run dev:clear`
4. Testez avec `npm run test-logs`
