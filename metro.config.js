// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Configuration pour améliorer les logs et le debugging
config.server = {
  ...config.server,
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Log des requêtes pour le debugging
      if (req.url && !req.url.includes('hot-reload')) {
        console.log(`[Metro] ${req.method} ${req.url}`);
      }
      return middleware(req, res, next);
    };
  },
};

// Resolve JSON parsing issues
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    keep_classnames: true,
    keep_fnames: true,
    mangle: {
      ...config.transformer.minifierConfig?.mangle,
      keep_classnames: true,
      keep_fnames: true
    },
    output: {
      ...config.transformer.minifierConfig?.output,
      ascii_only: true
    }
  }
};

// Add support for additional file extensions and platform-specific extensions
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'mjs',
  'cjs',
  'web.js',
  'web.ts',
  'web.tsx'
];

// Ensure platform-specific extensions are properly handled
config.resolver.platforms = ['web', 'ios', 'android'];

// Ajout de la configuration pour SDK 53
config.resolver.assetExts = [...config.resolver.assetExts, 'lottie'];

// Add Node.js polyfills for React Native
config.resolver.alias = {
  ...config.resolver.alias,
  stream: 'stream-browserify',
  crypto: 'react-native-crypto',
  buffer: '@craftzdog/react-native-buffer',
  util: 'util',
  url: 'react-native-url-polyfill',
  querystring: 'querystring-es3',
  path: 'path-browserify',
  fs: false,
  net: false,
  tls: false,
  ws: false,
  // Additional WebSocket and Node.js module polyfills
  'ws/lib/stream': false,
  'ws/lib/websocket': false,
  'ws/lib/buffer-util': false,
  'ws/lib/validation': false,
  'ws/lib/extension': false,
  'ws/lib/permessage-deflate': false,
  'ws/lib/sender': false,
  'ws/lib/receiver': false,
  'ws/lib/event-target': false,
  'ws/lib/constants': false,
  'ws/lib/subprotocol': false,
  'ws/lib/websocket-server': false,
  'ws/lib/limiter': false,
  http: false,
  https: false,
  zlib: false,
  os: false,
  child_process: false,
  cluster: false,
  dgram: false,
  dns: false,
  domain: false,
  events: 'events',
  readline: false,
  repl: false,
  string_decoder: false,
  sys: false,
  timers: false,
  tty: false,
  vm: false,
  worker_threads: false,
};

// Block problematic modules from being resolved
config.resolver.blockList = [
  // Block all ws library files
  /node_modules[/\\]ws[/\\]/,
  // Block specific problematic files
  /node_modules[/\\]ws[/\\]lib[/\\]stream\.js$/,
  /node_modules[/\\]ws[/\\]lib[/\\]websocket\.js$/,
  /node_modules[/\\]ws[/\\]lib[/\\].*\.js$/,
  /node_modules[/\\]ws[/\\]index\.js$/,
];

// Custom resolver to handle ws module imports and redirect them
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Log resolver calls for debugging (only in development)
  if (__DEV__ && moduleName.includes('ws')) {
    console.log(`[Metro Resolver] ${moduleName} from ${context.originModulePath || 'unknown'}`);
  }

  // Completely block ws module and its submodules
  if (moduleName === 'ws' ||
      moduleName.startsWith('ws/') ||
      moduleName.includes('/ws/') ||
      moduleName.endsWith('/ws') ||
      moduleName === 'ws/lib/stream' ||
      moduleName === 'ws/lib/websocket' ||
      moduleName.includes('ws/lib/')) {
    if (__DEV__) {
      console.log(`[Metro] Redirecting ${moduleName} to polyfill`);
    }
    return {
      filePath: __dirname + '/ws-polyfill/index.js',
      type: 'sourceFile',
    };
  }

  // Block stream module imports specifically from ws directory
  if (moduleName === 'stream' && context.originModulePath) {
    const originPath = context.originModulePath.replace(/\\/g, '/');
    if (originPath.includes('node_modules/ws/') ||
        originPath.includes('/ws/lib/') ||
        originPath.includes('ws\\lib\\') ||
        originPath.endsWith('/ws/index.js') ||
        originPath.endsWith('\\ws\\index.js')) {
      if (__DEV__) {
        console.log(`[Metro] Redirecting stream import from ws to polyfill`);
      }
      return {
        filePath: __dirname + '/ws-polyfill/index.js',
        type: 'sourceFile',
      };
    }
  }

  // Block any Node.js modules imported from ws
  if (context.originModulePath &&
      (context.originModulePath.includes('node_modules/ws/') ||
       context.originModulePath.includes('node_modules\\ws\\')) &&
      (moduleName === 'stream' ||
       moduleName === 'http' ||
       moduleName === 'https' ||
       moduleName === 'crypto' ||
       moduleName === 'zlib' ||
       moduleName === 'buffer' ||
       moduleName === 'util' ||
       moduleName === 'events')) {
    if (__DEV__) {
      console.log(`[Metro] Redirecting Node.js module ${moduleName} from ws to polyfill`);
    }
    return {
      filePath: __dirname + '/ws-polyfill/index.js',
      type: 'sourceFile',
    };
  }

  // Use default resolver for other modules
  return context.resolveRequest(context, moduleName, platform);
};

module.exports = config;

