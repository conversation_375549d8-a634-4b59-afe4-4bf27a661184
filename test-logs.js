/**
 * Script de test pour vérifier que les logs fonctionnent correctement
 * Exécuter avec: node test-logs.js
 */

console.log('🧪 Testing console functions...');
console.log('=' .repeat(50));

// Test des différents niveaux de log
console.log('📝 Testing console.log');
console.info('ℹ️ Testing console.info');
console.warn('⚠️ Testing console.warn');
console.error('🚨 Testing console.error');
console.debug('🐛 Testing console.debug');

// Test avec des objets
console.log('📦 Testing with objects:', {
  test: 'value',
  number: 42,
  boolean: true,
  array: [1, 2, 3],
  nested: {
    key: 'nested value'
  }
});

// Test avec des erreurs
try {
  throw new Error('Test error for logging');
} catch (error) {
  console.error('🚨 Caught error:', error);
}

console.log('=' .repeat(50));
console.log('✅ Console test completed');

// Instructions pour l'utilisateur
console.log('\n📋 INSTRUCTIONS:');
console.log('1. Si vous voyez tous les messages ci-dessus, les logs fonctionnent');
console.log('2. Lancez votre app avec: npm run dev');
console.log('3. Ouvrez Expo Go et connectez-vous à votre app');
console.log('4. Vérifiez que les logs apparaissent dans le terminal');
console.log('5. Si les logs n\'apparaissent pas, vérifiez:');
console.log('   - Que vous êtes en mode développement');
console.log('   - Que le terminal n\'est pas filtré');
console.log('   - Que Expo Go est bien connecté au serveur de développement');
