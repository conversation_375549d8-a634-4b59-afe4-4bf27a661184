// Test script to verify the routing fix
const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Routing Fix - Redirection to Home Page\n');

// 1. Check if problematic emergency redirect files were removed
console.log('1️⃣ Checking removed emergency redirect files...');
const removedFiles = [
  'app/emergency.tsx',
  'app/(app)/emergency.tsx'
];

for (const file of removedFiles) {
  if (!fs.existsSync(file)) {
    console.log(`✅ ${file} successfully removed`);
  } else {
    console.log(`❌ ${file} still exists (should be removed)`);
  }
}

// 2. Check if main index.tsx was created
console.log('\n2️⃣ Checking main index.tsx...');
if (fs.existsSync('app/index.tsx')) {
  console.log('✅ app/index.tsx created');
  
  const indexContent = fs.readFileSync('app/index.tsx', 'utf8');
  const checks = [
    { name: 'Auth context import', pattern: /useAuth/ },
    { name: 'Redirect to tabs', pattern: /Redirect.*\(tabs\)/ },
    { name: 'Redirect to login', pattern: /Redirect.*login/ },
    { name: 'Loading state', pattern: /ActivityIndicator/ }
  ];
  
  for (const check of checks) {
    if (check.pattern.test(indexContent)) {
      console.log(`✅ ${check.name} implemented`);
    } else {
      console.log(`❌ ${check.name} missing`);
    }
  }
} else {
  console.log('❌ app/index.tsx not found');
}

// 3. Check tabs layout configuration
console.log('\n3️⃣ Checking tabs layout...');
if (fs.existsSync('app/(tabs)/_layout.tsx')) {
  console.log('✅ Tabs layout exists');
  
  const tabsLayout = fs.readFileSync('app/(tabs)/_layout.tsx', 'utf8');
  if (tabsLayout.includes('name="index"') && tabsLayout.includes('title: \'Accueil\'')) {
    console.log('✅ Home tab (index) configured as first tab');
  } else {
    console.log('❌ Home tab configuration missing');
  }
} else {
  console.log('❌ Tabs layout not found');
}

// 4. Check home page exists
console.log('\n4️⃣ Checking home page...');
if (fs.existsSync('app/(tabs)/index.tsx')) {
  console.log('✅ Home page (app/(tabs)/index.tsx) exists');
} else {
  console.log('❌ Home page not found');
}

// 5. Check app/(app)/index.tsx redirection
console.log('\n5️⃣ Checking app group redirection...');
if (fs.existsSync('app/(app)/index.tsx')) {
  const appIndex = fs.readFileSync('app/(app)/index.tsx', 'utf8');
  if (appIndex.includes('Redirect href="../(tabs)"')) {
    console.log('✅ App group redirects to tabs');
  } else {
    console.log('❌ App group redirection missing or incorrect');
  }
} else {
  console.log('❌ app/(app)/index.tsx not found');
}

// 6. Check auth context for proper redirection
console.log('\n6️⃣ Checking auth context...');
if (fs.existsSync('context/auth.tsx')) {
  const authContext = fs.readFileSync('context/auth.tsx', 'utf8');
  
  // Check that there's no hardcoded redirection to emergency
  if (authContext.includes('emergency') || authContext.includes('Emergency')) {
    console.log('⚠️  Auth context contains emergency references (check for unwanted redirections)');
  } else {
    console.log('✅ Auth context clean of emergency redirections');
  }
  
  // Check for proper logout redirection
  if (authContext.includes('router.replace(\'/login\'')) {
    console.log('✅ Logout redirects to login page');
  } else {
    console.log('⚠️  Logout redirection may need verification');
  }
} else {
  console.log('❌ Auth context not found');
}

// 7. Check for any remaining emergency redirections
console.log('\n7️⃣ Checking for remaining emergency redirections...');
const filesToCheck = [
  'app/(auth)/login.tsx',
  'app/(auth)/register.tsx',
  'context/auth.tsx'
];

let emergencyRefsFound = false;
for (const file of filesToCheck) {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    const emergencyMatches = content.match(/emergency|Emergency/g);
    if (emergencyMatches && emergencyMatches.length > 0) {
      console.log(`⚠️  ${file} contains ${emergencyMatches.length} emergency reference(s)`);
      emergencyRefsFound = true;
    }
  }
}

if (!emergencyRefsFound) {
  console.log('✅ No unwanted emergency redirections found');
}

// 8. Final assessment
console.log('\n🎯 Final Assessment:');
console.log('=====================================');

const mainIndexExists = fs.existsSync('app/index.tsx');
const homePageExists = fs.existsSync('app/(tabs)/index.tsx');
const tabsLayoutExists = fs.existsSync('app/(tabs)/_layout.tsx');
const emergencyFilesRemoved = !fs.existsSync('app/emergency.tsx') && !fs.existsSync('app/(app)/emergency.tsx');

if (mainIndexExists && homePageExists && tabsLayoutExists && emergencyFilesRemoved) {
  console.log('🟢 ROUTING FIX SUCCESSFUL!');
  console.log('✅ Emergency redirect files removed');
  console.log('✅ Main index.tsx created with proper auth routing');
  console.log('✅ Home page exists and configured');
  console.log('✅ Tabs layout properly configured');
  console.log('✅ App should now redirect to home page instead of emergency');
} else {
  console.log('🟡 PARTIAL ROUTING FIX');
  console.log('⚠️  Some components may need verification');
}

console.log('\n📝 Expected Behavior:');
console.log('• Unauthenticated users → Login page');
console.log('• Authenticated users → Home page (tabs/index)');
console.log('• No automatic redirection to emergency page');
console.log('• Emergency page accessible only via explicit navigation');

console.log('\n🔧 Routing Flow:');
console.log('app/index.tsx → Check auth → /(tabs) or /(auth)/login');
console.log('app/(app)/index.tsx → Redirect to ../(tabs)');
console.log('app/(tabs)/_layout.tsx → Display tabs with index as home');
console.log('app/(tabs)/index.tsx → Main home page content');

console.log('\n💡 Testing:');
console.log('1. Open the app in browser or device');
console.log('2. If not logged in, should see login page');
console.log('3. After login, should see home page with tabs');
console.log('4. Emergency page should only be accessible via navigation');
