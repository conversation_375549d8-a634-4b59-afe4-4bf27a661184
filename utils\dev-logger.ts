/**
 * Configuration et utilitaires pour les logs de développement
 * Ce fichier améliore l'affichage des logs dans Expo Go
 */

import { Platform } from 'react-native';

// Types pour les niveaux de log
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'success';

// Configuration des logs
export const LOG_CONFIG = {
  enabled: __DEV__,
  showTimestamp: true,
  showPlatform: true,
  colorEnabled: true,
  maxLogHistory: 100
};

// Historique des logs pour le debugging
let logHistory: Array<{
  level: LogLevel;
  message: string;
  timestamp: Date;
  data?: any;
}> = [];

// Couleurs et emojis pour les différents niveaux
const LOG_STYLES = {
  debug: { emoji: '🐛', prefix: '[DEBUG]' },
  info: { emoji: 'ℹ️', prefix: '[INFO]' },
  warn: { emoji: '⚠️', prefix: '[WARN]' },
  error: { emoji: '🚨', prefix: '[ERROR]' },
  success: { emoji: '✅', prefix: '[SUCCESS]' }
};

/**
 * Logger amélioré pour le développement
 */
export class DevLogger {
  private static instance: DevLogger;

  static getInstance(): DevLogger {
    if (!DevLogger.instance) {
      DevLogger.instance = new DevLogger();
    }
    return DevLogger.instance;
  }

  private formatMessage(level: LogLevel, message: string): string {
    const style = LOG_STYLES[level];
    let formattedMessage = '';

    if (LOG_CONFIG.showTimestamp) {
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
      formattedMessage += `[${timestamp}] `;
    }

    if (LOG_CONFIG.showPlatform) {
      formattedMessage += `[${Platform.OS.toUpperCase()}] `;
    }

    formattedMessage += `${style.emoji} ${style.prefix} ${message}`;

    return formattedMessage;
  }

  private addToHistory(level: LogLevel, message: string, data?: any): void {
    logHistory.push({
      level,
      message,
      timestamp: new Date(),
      data
    });

    // Garder seulement les derniers logs
    if (logHistory.length > LOG_CONFIG.maxLogHistory) {
      logHistory = logHistory.slice(-LOG_CONFIG.maxLogHistory);
    }
  }

  debug(message: string, data?: any): void {
    if (!LOG_CONFIG.enabled) return;

    const formattedMessage = this.formatMessage('debug', message);
    console.debug(formattedMessage, data || '');
    this.addToHistory('debug', message, data);
  }

  info(message: string, data?: any): void {
    if (!LOG_CONFIG.enabled) return;

    const formattedMessage = this.formatMessage('info', message);
    console.info(formattedMessage, data || '');
    this.addToHistory('info', message, data);
  }

  warn(message: string, data?: any): void {
    const formattedMessage = this.formatMessage('warn', message);
    console.warn(formattedMessage, data || '');
    this.addToHistory('warn', message, data);
  }

  error(message: string, data?: any): void {
    const formattedMessage = this.formatMessage('error', message);
    console.error(formattedMessage, data || '');
    this.addToHistory('error', message, data);
  }

  success(message: string, data?: any): void {
    if (!LOG_CONFIG.enabled) return;

    const formattedMessage = this.formatMessage('success', message);
    console.log(formattedMessage, data || '');
    this.addToHistory('success', message, data);
  }

  /**
   * Log avec niveau personnalisé
   */
  log(level: LogLevel, message: string, data?: any): void {
    switch (level) {
      case 'debug':
        this.debug(message, data);
        break;
      case 'info':
        this.info(message, data);
        break;
      case 'warn':
        this.warn(message, data);
        break;
      case 'error':
        this.error(message, data);
        break;
      case 'success':
        this.success(message, data);
        break;
    }
  }

  /**
   * Afficher l'historique des logs
   */
  showHistory(count: number = 20): void {
    if (!LOG_CONFIG.enabled) return;

    console.log('\n📋 LOG HISTORY (Last ' + count + ' entries):');
    console.log('=' .repeat(50));
    
    const recentLogs = logHistory.slice(-count);
    recentLogs.forEach((log, index) => {
      const timestamp = log.timestamp.toISOString().split('T')[1].split('.')[0];
      const style = LOG_STYLES[log.level];
      console.log(`${index + 1}. [${timestamp}] ${style.emoji} ${log.message}`);
      if (log.data) {
        console.log('   Data:', log.data);
      }
    });
    console.log('=' .repeat(50) + '\n');
  }

  /**
   * Nettoyer l'historique
   */
  clearHistory(): void {
    logHistory = [];
    if (LOG_CONFIG.enabled) {
      console.log('🧹 Log history cleared');
    }
  }

  /**
   * Test des logs
   */
  testLogs(): void {
    if (!LOG_CONFIG.enabled) return;

    console.log('\n🧪 TESTING LOGGER FUNCTIONALITY:');
    console.log('=' .repeat(40));
    
    this.debug('This is a debug message', { test: 'debug data' });
    this.info('This is an info message', { test: 'info data' });
    this.warn('This is a warning message', { test: 'warn data' });
    this.error('This is an error message', { test: 'error data' });
    this.success('This is a success message', { test: 'success data' });
    
    console.log('=' .repeat(40));
    console.log('✅ Logger test completed\n');
  }

  /**
   * Obtenir les statistiques des logs
   */
  getStats(): any {
    const stats = {
      total: logHistory.length,
      byLevel: {
        debug: 0,
        info: 0,
        warn: 0,
        error: 0,
        success: 0
      },
      lastHour: 0
    };

    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    logHistory.forEach(log => {
      stats.byLevel[log.level]++;
      if (log.timestamp > oneHourAgo) {
        stats.lastHour++;
      }
    });

    return stats;
  }
}

// Instance singleton
export const devLogger = DevLogger.getInstance();

// Fonctions utilitaires pour un usage rapide
export const logDebug = (message: string, data?: any) => devLogger.debug(message, data);
export const logInfo = (message: string, data?: any) => devLogger.info(message, data);
export const logWarn = (message: string, data?: any) => devLogger.warn(message, data);
export const logError = (message: string, data?: any) => devLogger.error(message, data);
export const logSuccess = (message: string, data?: any) => devLogger.success(message, data);

/**
 * Initialiser le logger de développement
 */
export const initDevLogger = (): void => {
  if (!LOG_CONFIG.enabled) return;

  console.log('\n🚀 BOMOKO MOBILE - DEV LOGGER INITIALIZED');
  console.log('=' .repeat(50));
  console.log(`Platform: ${Platform.OS} ${Platform.Version}`);
  console.log(`Development Mode: ${__DEV__}`);
  console.log(`Timestamp: ${new Date().toISOString()}`);
  console.log('=' .repeat(50));

  // Test initial
  devLogger.testLogs();

  // Exposer le logger globalement pour le debugging
  if (typeof global !== 'undefined') {
    (global as any).devLogger = devLogger;
    (global as any).showLogHistory = () => devLogger.showHistory();
    (global as any).clearLogHistory = () => devLogger.clearHistory();
    (global as any).logStats = () => {
      const stats = devLogger.getStats();
      console.log('📊 Log Statistics:', stats);
      return stats;
    };
  }

  logSuccess('Dev Logger initialized successfully!');
};
